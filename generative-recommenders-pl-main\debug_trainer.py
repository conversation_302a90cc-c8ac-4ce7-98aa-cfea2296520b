import hydra
from omegaconf import DictConfig, OmegaConf
import lightning as L
import torch

@hydra.main(version_base="1.3", config_path="configs", config_name="train.yaml")
def debug_trainer(cfg: DictConfig):
    print("=== 配置检查 ===")
    print(f"trainer配置: {OmegaConf.to_yaml(cfg.trainer)}")
    
    # 实例化trainer
    trainer: L.Trainer = hydra.utils.instantiate(cfg.trainer)
    
    print(f"\n=== Trainer信息 ===")
    print(f"加速器: {trainer.accelerator}")
    print(f"设备: {trainer.strategy.root_device}")
    print(f"设备数量: {trainer.num_devices}")
    print(f"精度: {trainer.precision}")
    
    # 检查是否真的在GPU上
    if hasattr(trainer.strategy, 'root_device'):
        device = trainer.strategy.root_device
        print(f"实际设备: {device}")
        if device.type == 'cuda':
            print("✓ 配置为GPU训练")
        else:
            print("✗ 实际使用CPU训练")

if __name__ == "__main__":
    debug_trainer()
