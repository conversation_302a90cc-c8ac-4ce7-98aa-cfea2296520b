<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程任务测试页面</title>
    <link rel="stylesheet" href="./CSS/主页.css">
    <link rel="stylesheet" href="./libs/fontawesome.css">
</head>
<body>
    <div style="padding: 20px;">
        <h1>流程任务测试页面</h1>
        
        <!-- 任务总表 -->
        <div class="card" id="processTaskSummaryCard">
            <div class="card-header">
                <h3 class="card-title">任务总表</h3>
                <div class="card-actions" style="display: flex; gap: 8px;">
                    <button id="addProcessTaskBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> 新增任务
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="processTaskSummaryTable">
                    <thead>
                        <tr>
                            <th>合同编号</th>
                            <th>订单编号</th>
                            <th>证书类型</th>
                            <th>任务编号</th>
                            <th>合同金额</th>
                            <th>已收金额</th>
                            <th>所属机构</th>
                            <th>流程</th>
                            <th>科组</th>
                            <th>任务状态</th>
                            <th>工况比</th>
                            <th>创建时间</th>
                            <th>负责人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="processTaskSummaryTableBody">
                        <!-- 数据由JS渲染 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 任务总表数据结构
        let processTaskSummary = [
            {
                id: 1,
                contractNo: 'AA5634',
                orderNo: 'AAC3421',
                certType: '3C认证',
                taskNo: 'AACN2721',
                contractAmount: 12300,
                receivedAmount: 8450,
                organization: '京分中心',
                process: '认证一部',
                department: '未封闭',
                taskStatus: '未封闭',
                workRatio: '56.89%',
                createTime: '2025/1/12',
                responsible: '王勇',
                level1Tasks: [
                    {
                        id: 11,
                        taskName: '市场开发',
                        weight: 0.1,
                        amount: 1230,
                        progress: 100,
                        responsible: '王勇',
                        taskStatus: '已完成',
                        level2Tasks: [
                            {
                                id: 111,
                                taskName: '客户准护',
                                weight: 0.1,
                                amount: 123,
                                progress: 100,
                                responsible: '王勇',
                                taskStatus: '已完成'
                            }
                        ]
                    },
                    {
                        id: 12,
                        taskName: '证书服务',
                        weight: 0.1,
                        amount: 1230,
                        progress: 60,
                        responsible: '王勇',
                        taskStatus: '未完成',
                        level2Tasks: []
                    }
                ]
            }
        ];

        // 渲染任务总表
        function renderProcessTaskSummaryTable() {
            const tbody = document.getElementById('processTaskSummaryTableBody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            processTaskSummary.forEach(task => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${task.contractNo}</td>
                    <td>${task.orderNo}</td>
                    <td>${task.certType}</td>
                    <td>${task.taskNo}</td>
                    <td>¥${task.contractAmount.toLocaleString()}</td>
                    <td>¥${task.receivedAmount.toLocaleString()}</td>
                    <td>${task.organization}</td>
                    <td>${task.process}</td>
                    <td>${task.department}</td>
                    <td>${task.taskStatus}</td>
                    <td>${task.workRatio}</td>
                    <td>${task.createTime}</td>
                    <td>${task.responsible}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-mini" onclick="editProcessTask(${task.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-mini" onclick="deleteProcessTask(${task.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                            <button class="expand-btn" onclick="toggleLevel1Tasks(${task.id}, this)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(tr);
                
                // 添加一级流程任务行（初始隐藏）
                const level1Row = document.createElement('tr');
                level1Row.className = 'sub-table-row';
                level1Row.id = `level1-${task.id}`;
                level1Row.innerHTML = `
                    <td colspan="14">
                        <div class="sub-table-container">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px;">
                                <h4>一级流程任务</h4>
                                <button class="add-sub-task-btn" onclick="addLevel1Task(${task.id})">
                                    <i class="fas fa-plus"></i> 新增一级任务
                                </button>
                            </div>
                            <table class="sub-table">
                                <thead>
                                    <tr>
                                        <th>流程编号</th>
                                        <th>任务名称</th>
                                        <th>权重</th>
                                        <th>金额</th>
                                        <th>完成率</th>
                                        <th>责任人</th>
                                        <th>任务状态</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="level1-tbody-${task.id}">
                                    <!-- 一级任务数据 -->
                                </tbody>
                            </table>
                        </div>
                    </td>
                `;
                tbody.appendChild(level1Row);
            });
        }

        // 展开/收起一级任务
        function toggleLevel1Tasks(taskId, btn) {
            const level1Row = document.getElementById(`level1-${taskId}`);
            const icon = btn.querySelector('i');
            
            if (level1Row.classList.contains('show')) {
                level1Row.classList.remove('show');
                icon.className = 'fas fa-chevron-right';
                btn.classList.remove('expanded');
            } else {
                level1Row.classList.add('show');
                icon.className = 'fas fa-chevron-down';
                btn.classList.add('expanded');
                // 渲染一级任务数据
                renderLevel1Tasks(taskId);
            }
        }

        // 渲染一级任务
        function renderLevel1Tasks(taskId) {
            const task = processTaskSummary.find(t => t.id === taskId);
            if (!task) return;
            
            const tbody = document.getElementById(`level1-tbody-${taskId}`);
            tbody.innerHTML = '';
            
            task.level1Tasks.forEach(level1Task => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${level1Task.id}</td>
                    <td>${level1Task.taskName}</td>
                    <td>${(level1Task.weight * 100).toFixed(1)}%</td>
                    <td>¥${level1Task.amount.toLocaleString()}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${level1Task.progress}%"></div>
                            <div class="progress-text">${level1Task.progress}%</div>
                        </div>
                    </td>
                    <td>${level1Task.responsible}</td>
                    <td>${level1Task.taskStatus}</td>
                    <td>-</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-mini" onclick="editLevel1Task(${taskId}, ${level1Task.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-mini" onclick="deleteLevel1Task(${taskId}, ${level1Task.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                            <button class="expand-btn" onclick="toggleLevel2Tasks(${taskId}, ${level1Task.id}, this)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(tr);
                
                // 添加二级任务行（初始隐藏）
                const level2Row = document.createElement('tr');
                level2Row.className = 'sub-table-row';
                level2Row.id = `level2-${taskId}-${level1Task.id}`;
                level2Row.innerHTML = `
                    <td colspan="9">
                        <div class="sub-sub-table-container">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px;">
                                <h5>二级流程任务</h5>
                                <button class="add-sub-task-btn" onclick="addLevel2Task(${taskId}, ${level1Task.id})">
                                    <i class="fas fa-plus"></i> 新增二级任务
                                </button>
                            </div>
                            <table class="sub-sub-table">
                                <thead>
                                    <tr>
                                        <th>流程编号</th>
                                        <th>任务名称</th>
                                        <th>权重</th>
                                        <th>金额</th>
                                        <th>完成率</th>
                                        <th>责任人</th>
                                        <th>任务状态</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="level2-tbody-${taskId}-${level1Task.id}">
                                    <!-- 二级任务数据 -->
                                </tbody>
                            </table>
                        </div>
                    </td>
                `;
                tbody.appendChild(level2Row);
            });
        }

        // 展开/收起二级任务
        function toggleLevel2Tasks(taskId, level1TaskId, btn) {
            const level2Row = document.getElementById(`level2-${taskId}-${level1TaskId}`);
            const icon = btn.querySelector('i');

            if (level2Row.classList.contains('show')) {
                level2Row.classList.remove('show');
                icon.className = 'fas fa-chevron-right';
                btn.classList.remove('expanded');
            } else {
                level2Row.classList.add('show');
                icon.className = 'fas fa-chevron-down';
                btn.classList.add('expanded');
                // 渲染二级任务数据
                renderLevel2Tasks(taskId, level1TaskId);
            }
        }

        // 渲染二级任务
        function renderLevel2Tasks(taskId, level1TaskId) {
            const task = processTaskSummary.find(t => t.id === taskId);
            if (!task) return;

            const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
            if (!level1Task) return;

            const tbody = document.getElementById(`level2-tbody-${taskId}-${level1TaskId}`);
            tbody.innerHTML = '';

            level1Task.level2Tasks.forEach(level2Task => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${level2Task.id}</td>
                    <td>${level2Task.taskName}</td>
                    <td>${(level2Task.weight * 100).toFixed(1)}%</td>
                    <td>¥${level2Task.amount.toLocaleString()}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${level2Task.progress}%"></div>
                            <div class="progress-text">${level2Task.progress}%</div>
                        </div>
                    </td>
                    <td>${level2Task.responsible}</td>
                    <td>${level2Task.taskStatus}</td>
                    <td>-</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-mini" onclick="editLevel2Task(${taskId}, ${level1TaskId}, ${level2Task.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-mini" onclick="deleteLevel2Task(${taskId}, ${level1TaskId}, ${level2Task.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 其他函数的简单实现
        function editProcessTask(taskId) { alert(`编辑任务: ${taskId}`); }
        function deleteProcessTask(taskId) { alert(`删除任务: ${taskId}`); }
        function addLevel1Task(taskId) { alert(`新增一级任务: ${taskId}`); }
        function editLevel1Task(taskId, level1TaskId) { alert(`编辑一级任务: ${level1TaskId}`); }
        function deleteLevel1Task(taskId, level1TaskId) { alert(`删除一级任务: ${level1TaskId}`); }
        function addLevel2Task(taskId, level1TaskId) { alert(`新增二级任务: ${level1TaskId}`); }
        function editLevel2Task(taskId, level1TaskId, level2TaskId) { alert(`编辑二级任务: ${level2TaskId}`); }
        function deleteLevel2Task(taskId, level1TaskId, level2TaskId) { alert(`删除二级任务: ${level2TaskId}`); }

        // 新增任务总表任务按钮事件
        document.getElementById('addProcessTaskBtn').onclick = function() {
            alert('新增任务总表任务');
        };

        // 页面加载完成后渲染表格
        window.onload = function() {
            renderProcessTaskSummaryTable();
        };
    </script>
</body>
</html>
