<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程任务测试页面</title>
    <link rel="stylesheet" href="CSS/主页.css">
    <link rel="stylesheet" href="libs/fontawesome.css">
</head>
<body>
    <div class="container">
        <h1>流程任务界面测试</h1>
        
        <!-- 任务总表 -->
        <div class="card" id="processTaskSummaryCard">
            <div class="card-header">
                <h3 class="card-title">任务总表</h3>
                <div class="card-actions" style="display: flex; gap: 8px;">
                    <button id="addProcessTaskBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> 新增任务
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="processTaskSummaryTable">
                    <thead>
                        <tr>
                            <th>合同编号</th>
                            <th>订单编号</th>
                            <th>证书类型</th>
                            <th>任务编号</th>
                            <th>合同金额</th>
                            <th>已收金额</th>
                            <th>所属机构</th>
                            <th>流程</th>
                            <th>科组</th>
                            <th>任务状态</th>
                            <th>工况比</th>
                            <th>创建时间</th>
                            <th>负责人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="processTaskSummaryTableBody">
                        <tr>
                            <td>AA5634</td>
                            <td>AAC3421</td>
                            <td>3C认证</td>
                            <td>AACN2721</td>
                            <td>12300</td>
                            <td>8450</td>
                            <td>分中心认证通一部</td>
                            <td>2</td>
                            <td>未封闭</td>
                            <td>56.89%</td>
                            <td>2025/1/12</td>
                            <td>王勇</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="editProcessTask(this)">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn delete-btn" onclick="deleteProcessTask(this)">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 一级流程 -->
        <div class="card" id="firstLevelProcessCard" style="margin-top: 20px;">
            <div class="card-header">
                <h3 class="card-title">一级流程</h3>
                <div class="card-actions" style="display: flex; gap: 8px;">
                    <button id="addFirstLevelProcessBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> 新增流程
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="firstLevelProcessTable">
                    <thead>
                        <tr>
                            <th>流程编号</th>
                            <th>任务名称</th>
                            <th>权重</th>
                            <th>金额</th>
                            <th>完成率</th>
                            <th>责任人</th>
                            <th>任务完成时间</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="firstLevelProcessTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 二级流程 -->
        <div class="card" id="secondLevelProcessCard" style="margin-top: 20px;">
            <div class="card-header">
                <h3 class="card-title">二级流程</h3>
                <div class="card-actions" style="display: flex; gap: 8px;">
                    <button id="addSecondLevelProcessBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> 新增流程
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="secondLevelProcessTable">
                    <thead>
                        <tr>
                            <th>流程编号</th>
                            <th>任务名称</th>
                            <th>负责部门</th>
                            <th>责任人</th>
                            <th>产值权重</th>
                            <th>金额</th>
                            <th>完成率</th>
                            <th>责任人</th>
                            <th>任务完成时间</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="secondLevelProcessTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 流程任务模态框 -->
    <div id="processTaskModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="processTaskModalTitle">新增流程任务</h3>
                <button class="close-modal" onclick="closeProcessTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>合同编号</label>
                    <input type="text" id="processContractId" class="form-control">
                </div>
                <div class="form-row">
                    <label>订单编号</label>
                    <input type="text" id="processOrderId" class="form-control">
                </div>
                <div class="form-row">
                    <label>证书类型</label>
                    <input type="text" id="processCertType" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务编号</label>
                    <input type="text" id="processTaskId" class="form-control">
                </div>
                <div class="form-row">
                    <label>合同金额</label>
                    <input type="number" id="processContractAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>已收金额</label>
                    <input type="number" id="processReceivedAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>所属机构</label>
                    <input type="text" id="processOrganization" class="form-control">
                </div>
                <div class="form-row">
                    <label>流程</label>
                    <input type="text" id="processFlow" class="form-control">
                </div>
                <div class="form-row">
                    <label>科组</label>
                    <input type="text" id="processGroup" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务状态</label>
                    <select id="processTaskStatus" class="form-control">
                        <option value="未封闭">未封闭</option>
                        <option value="已封闭">已封闭</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>工况比</label>
                    <input type="text" id="processWorkRatio" class="form-control">
                </div>
                <div class="form-row">
                    <label>创建时间</label>
                    <input type="date" id="processCreateTime" class="form-control">
                </div>
                <div class="form-row">
                    <label>负责人</label>
                    <input type="text" id="processResponsible" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeProcessTaskModal()">取消</button>
                <button class="btn" id="saveProcessTaskBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 一级流程模态框 -->
    <div id="firstLevelProcessModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="firstLevelProcessModalTitle">新增一级流程</h3>
                <button class="close-modal" onclick="closeFirstLevelProcessModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>流程编号</label>
                    <input type="text" id="firstLevelProcessId" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务名称</label>
                    <input type="text" id="firstLevelTaskName" class="form-control">
                </div>
                <div class="form-row">
                    <label>权重</label>
                    <input type="text" id="firstLevelWeight" class="form-control">
                </div>
                <div class="form-row">
                    <label>金额</label>
                    <input type="number" id="firstLevelAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>完成率</label>
                    <input type="text" id="firstLevelCompletionRate" class="form-control">
                </div>
                <div class="form-row">
                    <label>责任人</label>
                    <input type="text" id="firstLevelResponsible" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务完成时间</label>
                    <input type="date" id="firstLevelCompletionTime" class="form-control">
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <textarea id="firstLevelRemark" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeFirstLevelProcessModal()">取消</button>
                <button class="btn" id="saveFirstLevelProcessBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 二级流程模态框 -->
    <div id="secondLevelProcessModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:450px;">
            <div class="modal-header">
                <h3 class="modal-title" id="secondLevelProcessModalTitle">新增二级流程</h3>
                <button class="close-modal" onclick="closeSecondLevelProcessModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>流程编号</label>
                    <input type="text" id="secondLevelProcessId" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务名称</label>
                    <input type="text" id="secondLevelTaskName" class="form-control">
                </div>
                <div class="form-row">
                    <label>负责部门</label>
                    <input type="text" id="secondLevelDepartment" class="form-control">
                </div>
                <div class="form-row">
                    <label>责任人</label>
                    <input type="text" id="secondLevelResponsible" class="form-control">
                </div>
                <div class="form-row">
                    <label>产值权重</label>
                    <input type="text" id="secondLevelValueWeight" class="form-control">
                </div>
                <div class="form-row">
                    <label>金额</label>
                    <input type="number" id="secondLevelAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>完成率</label>
                    <input type="text" id="secondLevelCompletionRate" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务完成时间</label>
                    <input type="date" id="secondLevelCompletionTime" class="form-control">
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <textarea id="secondLevelRemark" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSecondLevelProcessModal()">取消</button>
                <button class="btn" id="saveSecondLevelProcessBtn">保存</button>
            </div>
        </div>
    </div>

    <script src="JS/主页.js"></script>
</body>
</html>
