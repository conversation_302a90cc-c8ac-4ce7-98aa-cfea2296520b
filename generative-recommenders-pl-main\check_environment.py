import torch
import os
import subprocess

print("=== 环境检查 ===")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"CUDA版本: {torch.version.cuda}")
print(f"cuDNN版本: {torch.backends.cudnn.version()}")

# 检查环境变量
print(f"\nCUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', '未设置')}")
print(f"PYTORCH_NVML_BASED_CUDA_CHECK: {os.environ.get('PYTORCH_NVML_BASED_CUDA_CHECK', '未设置')}")

if torch.cuda.is_available():
    print(f"\nGPU设备数量: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        props = torch.cuda.get_device_properties(i)
        print(f"  内存: {props.total_memory / 1e9:.1f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")

# 测试简单的GPU操作
if torch.cuda.is_available():
    try:
        print("\n=== GPU测试 ===")
        device = torch.device('cuda:0')
        x = torch.randn(100, 100, device=device)
        y = torch.randn(100, 100, device=device)
        z = torch.mm(x, y)
        print("✓ GPU计算测试成功")
        print(f"结果在设备: {z.device}")
    except Exception as e:
        print(f"✗ GPU计算测试失败: {e}")
else:
    print("✗ CUDA不可用")

# 检查nvidia-smi
try:
    result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
    if result.returncode == 0:
        print("\n=== nvidia-smi 输出 ===")
        print(result.stdout)
    else:
        print("nvidia-smi 命令失败")
except FileNotFoundError:
    print("nvidia-smi 命令未找到")