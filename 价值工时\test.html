<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .calendar-day {
            display: inline-block;
            width: 60px;
            height: 60px;
            border: 1px solid #ddd;
            margin: 2px;
            padding: 5px;
            cursor: pointer;
            background: white;
            border-radius: 4px;
        }
        .calendar-day:hover {
            background: #f0f0f0;
        }
        .calendar-day-note {
            font-size: 10px;
            color: #007bff;
            background: rgba(0, 123, 255, 0.1);
            padding: 1px 3px;
            border-radius: 2px;
            margin-top: 2px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        .form-row {
            margin-bottom: 15px;
        }
        .form-row label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
        }
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .edit-btn {
            background: #007bff;
            color: white;
        }
        .delete-btn {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <h1>价值工时系统功能测试</h1>
    
    <div class="test-section">
        <h2>1. 日历备注功能测试</h2>
        <p>点击下面的日期格子来测试备注功能：</p>
        <div class="calendar-day" onclick="showCalendarNote('2025-07-30', 8.5)">
            <div>30</div>
            <div style="font-size: 11px;">8.5h</div>
            <div class="calendar-day-note">测试备注</div>
        </div>
        <div class="calendar-day" onclick="showCalendarNote('2025-07-31', 9.0)">
            <div>31</div>
            <div style="font-size: 11px;">9.0h</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. 个人任务表格测试</h2>
        <h3>我的待办事项列表</h3>
        <button class="btn btn-primary" onclick="openTaskModal('todo')">新增流程</button>
        <table id="todoTable">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>审核状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>项目需求分析</td>
                    <td>执行中</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editTask(1)">编辑</button>
                        <button class="action-btn delete-btn" onclick="deleteTask(1)">删除</button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 日历备注模态框 -->
    <div id="noteModal" class="modal">
        <div class="modal-content">
            <h3>日期备注</h3>
            <div class="form-row">
                <label>日期</label>
                <input type="text" id="noteDate" class="form-control" readonly>
            </div>
            <div class="form-row">
                <label>工时</label>
                <input type="text" id="noteHours" class="form-control" readonly>
            </div>
            <div class="form-row">
                <label>备注</label>
                <textarea id="noteContent" class="form-control" rows="4"></textarea>
            </div>
            <button class="btn btn-primary" onclick="saveNote()">保存</button>
            <button class="btn btn-secondary" onclick="closeNoteModal()">取消</button>
        </div>
    </div>
    
    <!-- 任务模态框 -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <h3 id="taskModalTitle">新增流程</h3>
            <div class="form-row">
                <label>名称</label>
                <input type="text" id="taskName" class="form-control">
            </div>
            <div class="form-row">
                <label>审核状态</label>
                <select id="taskStatus" class="form-control">
                    <option value="执行中">执行中</option>
                    <option value="提交">提交</option>
                    <option value="审核">审核</option>
                    <option value="审批">审批</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="saveTask()">保存</button>
            <button class="btn btn-secondary" onclick="closeTaskModal()">取消</button>
        </div>
    </div>
    
    <script>
        // 测试数据
        const notes = {};
        let currentNoteDate = '';
        
        // 日历备注功能
        function showCalendarNote(date, hours) {
            currentNoteDate = date;
            document.getElementById('noteModal').style.display = 'flex';
            document.getElementById('noteDate').value = date;
            document.getElementById('noteHours').value = hours + '小时';
            document.getElementById('noteContent').value = notes[date] || '';
        }
        
        function closeNoteModal() {
            document.getElementById('noteModal').style.display = 'none';
        }
        
        function saveNote() {
            const content = document.getElementById('noteContent').value.trim();
            if (content) {
                notes[currentNoteDate] = content;
                alert('备注保存成功！');
            } else {
                delete notes[currentNoteDate];
                alert('备注已清空！');
            }
            closeNoteModal();
            // 这里可以更新日历显示
        }
        
        // 任务管理功能
        function openTaskModal(type) {
            document.getElementById('taskModal').style.display = 'flex';
            document.getElementById('taskModalTitle').textContent = '新增流程';
            document.getElementById('taskName').value = '';
            document.getElementById('taskStatus').value = '执行中';
        }
        
        function closeTaskModal() {
            document.getElementById('taskModal').style.display = 'none';
        }
        
        function saveTask() {
            const name = document.getElementById('taskName').value.trim();
            const status = document.getElementById('taskStatus').value;
            
            if (!name) {
                alert('请输入流程名称');
                return;
            }
            
            alert(`保存成功！\n名称：${name}\n状态：${status}`);
            closeTaskModal();
        }
        
        function editTask(id) {
            alert('编辑任务 ID: ' + id);
        }
        
        function deleteTask(id) {
            if (confirm('确定删除该流程？')) {
                alert('删除任务 ID: ' + id);
            }
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const noteModal = document.getElementById('noteModal');
            const taskModal = document.getElementById('taskModal');
            if (event.target === noteModal) {
                closeNoteModal();
            }
            if (event.target === taskModal) {
                closeTaskModal();
            }
        }
    </script>
</body>
</html>
