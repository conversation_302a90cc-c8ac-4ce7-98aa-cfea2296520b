<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务总表测试</title>
    <link rel="stylesheet" href="CSS/主页.css">
    <link rel="stylesheet" href="libs/fontawesome.css">
</head>
<body>
    <div class="container">
        <h1>任务总表测试页面</h1>
        
        <!-- 任务总表 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">任务总表</h3>
                <div class="card-actions" style="display: flex; gap: 8px;">
                    <button id="addSummaryTaskBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> 新增总任务
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="taskSummaryTable">
                    <thead>
                        <tr>
                            <th>任务号</th>
                            <th>费率</th>
                            <th>任务金额</th>
                            <th>计划工时</th>
                            <th>理论价值工时</th>
                            <th>填报工时汇总</th>
                            <th>确认价值工时</th>
                            <th>交通费用</th>
                            <th>住宿费用</th>
                            <th>其他费用</th>
                            <th>操作</th>
                            <th>查看</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>AA5634</td>
                            <td>AAC3421</td>
                            <td>¥12300</td>
                            <td>8450</td>
                            <td>8450</td>
                            <td>8450</td>
                            <td>8450</td>
                            <td>¥0</td>
                            <td>¥0</td>
                            <td>¥0</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="editSummaryTask(this)">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn delete-btn" onclick="deleteSummaryTask(this)">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                            <td>
                                <button class="btn btn-outline" onclick="viewProjectTasks()">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 任务总表 新增/编辑模态框 -->
    <div id="summaryTaskModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="summaryModalTitle">新增总任务</h3>
                <button class="close-modal" onclick="closeSummaryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>任务号</label><input type="text" id="summaryTaskId" class="form-control"></div>
                <div class="form-row"><label>费率</label><input type="number" id="summaryFeeRate" class="form-control"></div>
                <div class="form-row"><label>任务金额</label><input type="text" id="summaryAmount" class="form-control"></div>
                <div class="form-row"><label>计划工时</label><input type="number" id="summaryPlannedHours" class="form-control"></div>
                <div class="form-row"><label>理论价值工时</label><input type="number" id="summaryTheoryHours" class="form-control"></div>
                <div class="form-row"><label>填报工时汇总</label><input type="number" id="summaryReportedHours" class="form-control"></div>
                <div class="form-row"><label>确认价值工时</label><input type="number" id="summaryConfirmedHours" class="form-control"></div>
                <div class="form-row"><label>交通费用</label><input type="text" id="summaryTrafficFee" class="form-control"></div>
                <div class="form-row"><label>住宿费用</label><input type="text" id="summaryHotelFee" class="form-control"></div>
                <div class="form-row"><label>其他费用</label><input type="text" id="summaryOtherFee" class="form-control"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSummaryModal()">取消</button>
                <button class="btn" id="saveSummaryTaskBtn">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 测试函数
        function editSummaryTask(button) {
            alert('编辑功能测试');
        }
        
        function deleteSummaryTask(button) {
            if (confirm('确定要删除这个任务吗？')) {
                const row = button.closest('tr');
                row.remove();
            }
        }
        
        function viewProjectTasks() {
            alert('查看项目任务功能测试');
        }
        
        function closeSummaryModal() {
            document.getElementById('summaryTaskModal').style.display = 'none';
        }
        
        // 新增按钮事件
        document.getElementById('addSummaryTaskBtn').addEventListener('click', function() {
            document.getElementById('summaryTaskModal').style.display = 'block';
        });
    </script>
</body>
</html>
