<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套表格完整演示</title>
    <link rel="stylesheet" href="./CSS/主页.css">
    <link rel="stylesheet" href="./libs/fontawesome.css">
    <style>
        body {
            padding: 20px;
            background-color: #f1f5f9;
        }
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        .demo-header p {
            color: #64748b;
            font-size: 16px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #e0f2fe;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            color: #0369a1;
        }
        .instructions {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #f59e0b;
        }
        .instructions h3 {
            color: #92400e;
            margin-bottom: 10px;
        }
        .instructions ul {
            color: #92400e;
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>嵌套表格完整功能演示</h1>
            <p>展示三级嵌套表格：主任务 → 一级流程 → 二级流程</p>
            
            <div class="feature-list">
                <div class="feature-item">✓ 三级嵌套展开/收缩</div>
                <div class="feature-item">✓ 金额自动计算（权重×上级金额）</div>
                <div class="feature-item">✓ 模态框新增/编辑功能</div>
                <div class="feature-item">✓ 删除确认功能</div>
                <div class="feature-item">✓ 响应式设计</div>
                <div class="feature-item">✓ 美观的UI设计</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
                <li>点击左侧箭头按钮展开/收缩子表格</li>
                <li>点击"新增"按钮添加新的流程任务</li>
                <li>金额会根据权重自动计算（权重 × 上级金额）</li>
                <li>编辑和删除按钮提供完整的CRUD功能</li>
            </ul>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>任务总表（嵌套表格演示）</h3>
                <button class="btn btn-success" onclick="alert('新增主任务功能（可扩展）')">
                    <i class="fas fa-plus"></i> 新增任务
                </button>
            </div>
            <div class="card-body">
                <table class="expandable-table">
                    <thead>
                        <tr>
                            <th width="30px"></th>
                            <th>合同编号</th>
                            <th>订单编号</th>
                            <th>证书类型</th>
                            <th>任务编号</th>
                            <th>合同金额</th>
                            <th>已收金额</th>
                            <th>所属机构</th>
                            <th>流程</th>
                            <th>任务状态</th>
                            <th>工况比</th>
                            <th>创建时间</th>
                            <th>负责人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 第一个主任务 -->
                        <tr class="main-task-row" data-task-id="AA5634">
                            <td>
                                <button class="expand-btn" onclick="toggleFirstLevelProcess('AA5634')">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </td>
                            <td>AA5634</td>
                            <td>AAC3421</td>
                            <td>3C认证</td>
                            <td>AACN2721</td>
                            <td>12300</td>
                            <td>8450</td>
                            <td>分中心认证通一部</td>
                            <td>2</td>
                            <td>未封闭</td>
                            <td>56.89%</td>
                            <td>2025/1/12</td>
                            <td>王勇</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="alert('编辑主任务: AA5634')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn delete-btn" onclick="confirmDelete('主任务', 'AA5634')">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                        </tr>
                        <!-- 一级流程子表 -->
                        <tr class="first-level-container" id="firstLevel_AA5634" style="display: none;">
                            <td colspan="14">
                                <div class="sub-table-container">
                                    <div class="sub-table-header">
                                        <h4>一级流程任务</h4>
                                        <button class="btn btn-sm btn-success" onclick="alert('新增一级流程功能（可扩展）')">
                                            <i class="fas fa-plus"></i> 新增一级流程
                                        </button>
                                    </div>
                                    <table class="sub-table first-level-table">
                                        <thead>
                                            <tr>
                                                <th width="30px"></th>
                                                <th>流程编号</th>
                                                <th>任务名称</th>
                                                <th>权重</th>
                                                <th>金额</th>
                                                <th>完成率</th>
                                                <th>责任人</th>
                                                <th>任务完成时间</th>
                                                <th>备注</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="first-level-row" data-first-level-id="AA5634_1">
                                                <td>
                                                    <button class="expand-btn" onclick="toggleSecondLevelProcess('AA5634_1')">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </td>
                                                <td>1</td>
                                                <td>市场开发</td>
                                                <td>10%</td>
                                                <td>1230</td>
                                                <td>100%</td>
                                                <td>王勇</td>
                                                <td>2025-01-15</td>
                                                <td>已完成</td>
                                                <td>
                                                    <button class="action-btn edit-btn" onclick="alert('编辑一级流程: AA5634_1')">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="confirmDelete('一级流程', 'AA5634_1')">
                                                        <i class="fas fa-trash-alt"></i> 删除
                                                    </button>
                                                </td>
                                            </tr>
                                            <!-- 二级流程子表 -->
                                            <tr class="second-level-container" id="secondLevel_AA5634_1" style="display: none;">
                                                <td colspan="10">
                                                    <div class="sub-table-container level-2">
                                                        <div class="sub-table-header">
                                                            <h5>二级流程任务</h5>
                                                            <button class="btn btn-sm btn-success" onclick="alert('新增二级流程功能（可扩展）')">
                                                                <i class="fas fa-plus"></i> 新增二级流程
                                                            </button>
                                                        </div>
                                                        <table class="sub-table second-level-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>流程编号</th>
                                                                    <th>任务名称</th>
                                                                    <th>负责部门</th>
                                                                    <th>责任人</th>
                                                                    <th>产值权重</th>
                                                                    <th>金额</th>
                                                                    <th>完成率</th>
                                                                    <th>任务完成时间</th>
                                                                    <th>备注</th>
                                                                    <th>操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr data-second-level-id="AA5634_1_1">
                                                                    <td>1</td>
                                                                    <td>客户维护</td>
                                                                    <td>分中心检查部</td>
                                                                    <td>王勇</td>
                                                                    <td>10%</td>
                                                                    <td>123</td>
                                                                    <td>100%</td>
                                                                    <td>2025-01-10</td>
                                                                    <td>已完成</td>
                                                                    <td>
                                                                        <button class="action-btn edit-btn" onclick="alert('编辑二级流程: AA5634_1_1')">
                                                                            <i class="fas fa-edit"></i> 编辑
                                                                        </button>
                                                                        <button class="action-btn delete-btn" onclick="confirmDelete('二级流程', 'AA5634_1_1')">
                                                                            <i class="fas fa-trash-alt"></i> 删除
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                <tr data-second-level-id="AA5634_1_2">
                                                                    <td>2</td>
                                                                    <td>市场调研</td>
                                                                    <td>分中心检查部</td>
                                                                    <td>李云</td>
                                                                    <td>15%</td>
                                                                    <td>185</td>
                                                                    <td>80%</td>
                                                                    <td>2025-01-12</td>
                                                                    <td>进行中</td>
                                                                    <td>
                                                                        <button class="action-btn edit-btn" onclick="alert('编辑二级流程: AA5634_1_2')">
                                                                            <i class="fas fa-edit"></i> 编辑
                                                                        </button>
                                                                        <button class="action-btn delete-btn" onclick="confirmDelete('二级流程', 'AA5634_1_2')">
                                                                            <i class="fas fa-trash-alt"></i> 删除
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                            
                                            <!-- 第二个一级流程 -->
                                            <tr class="first-level-row" data-first-level-id="AA5634_2">
                                                <td>
                                                    <button class="expand-btn" onclick="toggleSecondLevelProcess('AA5634_2')">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </td>
                                                <td>2</td>
                                                <td>证书服务</td>
                                                <td>15%</td>
                                                <td>1845</td>
                                                <td>60%</td>
                                                <td>李云</td>
                                                <td>2025-01-20</td>
                                                <td>进行中</td>
                                                <td>
                                                    <button class="action-btn edit-btn" onclick="alert('编辑一级流程: AA5634_2')">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="confirmDelete('一级流程', 'AA5634_2')">
                                                        <i class="fas fa-trash-alt"></i> 删除
                                                    </button>
                                                </td>
                                            </tr>
                                            <!-- 第二个一级流程的二级流程子表 -->
                                            <tr class="second-level-container" id="secondLevel_AA5634_2" style="display: none;">
                                                <td colspan="10">
                                                    <div class="sub-table-container level-2">
                                                        <div class="sub-table-header">
                                                            <h5>二级流程任务</h5>
                                                            <button class="btn btn-sm btn-success" onclick="alert('新增二级流程功能（可扩展）')">
                                                                <i class="fas fa-plus"></i> 新增二级流程
                                                            </button>
                                                        </div>
                                                        <table class="sub-table second-level-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>流程编号</th>
                                                                    <th>任务名称</th>
                                                                    <th>负责部门</th>
                                                                    <th>责任人</th>
                                                                    <th>产值权重</th>
                                                                    <th>金额</th>
                                                                    <th>完成率</th>
                                                                    <th>任务完成时间</th>
                                                                    <th>备注</th>
                                                                    <th>操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr data-second-level-id="AA5634_2_1">
                                                                    <td>1</td>
                                                                    <td>证书申请</td>
                                                                    <td>分中心认证部</td>
                                                                    <td>李云</td>
                                                                    <td>20%</td>
                                                                    <td>369</td>
                                                                    <td>50%</td>
                                                                    <td>2025-01-18</td>
                                                                    <td>进行中</td>
                                                                    <td>
                                                                        <button class="action-btn edit-btn" onclick="alert('编辑二级流程: AA5634_2_1')">
                                                                            <i class="fas fa-edit"></i> 编辑
                                                                        </button>
                                                                        <button class="action-btn delete-btn" onclick="confirmDelete('二级流程', 'AA5634_2_1')">
                                                                            <i class="fas fa-trash-alt"></i> 删除
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- 第二个主任务 -->
                        <tr class="main-task-row" data-task-id="AA7637">
                            <td>
                                <button class="expand-btn" onclick="toggleFirstLevelProcess('AA7637')">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </td>
                            <td>AA7637</td>
                            <td>AAC6541</td>
                            <td>3C认证</td>
                            <td>AACN2711</td>
                            <td>34200</td>
                            <td>28000</td>
                            <td>分中心认证通一部</td>
                            <td>1</td>
                            <td>未封闭</td>
                            <td>67.37%</td>
                            <td>2025/1/13</td>
                            <td>张三</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="alert('编辑主任务: AA7637')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn delete-btn" onclick="confirmDelete('主任务', 'AA7637')">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                        </tr>
                        <!-- 第二个主任务的一级流程子表 -->
                        <tr class="first-level-container" id="firstLevel_AA7637" style="display: none;">
                            <td colspan="14">
                                <div class="sub-table-container">
                                    <div class="sub-table-header">
                                        <h4>一级流程任务</h4>
                                        <button class="btn btn-sm btn-success" onclick="alert('新增一级流程功能（可扩展）')">
                                            <i class="fas fa-plus"></i> 新增一级流程
                                        </button>
                                    </div>
                                    <table class="sub-table first-level-table">
                                        <thead>
                                            <tr>
                                                <th width="30px"></th>
                                                <th>流程编号</th>
                                                <th>任务名称</th>
                                                <th>权重</th>
                                                <th>金额</th>
                                                <th>完成率</th>
                                                <th>责任人</th>
                                                <th>任务完成时间</th>
                                                <th>备注</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="first-level-row" data-first-level-id="AA7637_1">
                                                <td>
                                                    <button class="expand-btn" onclick="toggleSecondLevelProcess('AA7637_1')">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </td>
                                                <td>1</td>
                                                <td>认证受理</td>
                                                <td>25%</td>
                                                <td>8550</td>
                                                <td>70%</td>
                                                <td>张三</td>
                                                <td>2025-01-25</td>
                                                <td>进行中</td>
                                                <td>
                                                    <button class="action-btn edit-btn" onclick="alert('编辑一级流程: AA7637_1')">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="confirmDelete('一级流程', 'AA7637_1')">
                                                        <i class="fas fa-trash-alt"></i> 删除
                                                    </button>
                                                </td>
                                            </tr>
                                            <!-- 二级流程子表 -->
                                            <tr class="second-level-container" id="secondLevel_AA7637_1" style="display: none;">
                                                <td colspan="10">
                                                    <div class="sub-table-container level-2">
                                                        <div class="sub-table-header">
                                                            <h5>二级流程任务</h5>
                                                            <button class="btn btn-sm btn-success" onclick="alert('新增二级流程功能（可扩展）')">
                                                                <i class="fas fa-plus"></i> 新增二级流程
                                                            </button>
                                                        </div>
                                                        <table class="sub-table second-level-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>流程编号</th>
                                                                    <th>任务名称</th>
                                                                    <th>负责部门</th>
                                                                    <th>责任人</th>
                                                                    <th>产值权重</th>
                                                                    <th>金额</th>
                                                                    <th>完成率</th>
                                                                    <th>任务完成时间</th>
                                                                    <th>备注</th>
                                                                    <th>操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr data-second-level-id="AA7637_1_1">
                                                                    <td>1</td>
                                                                    <td>资料审核</td>
                                                                    <td>分中心审核部</td>
                                                                    <td>张三</td>
                                                                    <td>30%</td>
                                                                    <td>2565</td>
                                                                    <td>80%</td>
                                                                    <td>2025-01-20</td>
                                                                    <td>进行中</td>
                                                                    <td>
                                                                        <button class="action-btn edit-btn" onclick="alert('编辑二级流程: AA7637_1_1')">
                                                                            <i class="fas fa-edit"></i> 编辑
                                                                        </button>
                                                                        <button class="action-btn delete-btn" onclick="confirmDelete('二级流程', 'AA7637_1_1')">
                                                                            <i class="fas fa-trash-alt"></i> 删除
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #64748b;">
            <p>演示完成！这个嵌套表格系统支持三级层次结构，具有完整的CRUD功能和金额自动计算。</p>
        </div>
    </div>

    <script>
        // 切换一级流程显示/隐藏
        function toggleFirstLevelProcess(taskId) {
            const container = document.getElementById(`firstLevel_${taskId}`);
            const button = document.querySelector(`[onclick="toggleFirstLevelProcess('${taskId}')"]`);
            
            if (container.style.display === 'none') {
                container.style.display = 'table-row';
                button.classList.add('expanded');
            } else {
                container.style.display = 'none';
                button.classList.remove('expanded');
                
                // 同时隐藏所有二级流程
                const secondLevelContainers = container.querySelectorAll('.second-level-container');
                secondLevelContainers.forEach(sc => {
                    sc.style.display = 'none';
                    const secondButton = sc.previousElementSibling.querySelector('.expand-btn');
                    if (secondButton) secondButton.classList.remove('expanded');
                });
            }
        }

        // 切换二级流程显示/隐藏
        function toggleSecondLevelProcess(firstLevelId) {
            const container = document.getElementById(`secondLevel_${firstLevelId}`);
            const button = document.querySelector(`[onclick="toggleSecondLevelProcess('${firstLevelId}')"]`);
            
            if (container.style.display === 'none') {
                container.style.display = 'table-row';
                button.classList.add('expanded');
            } else {
                container.style.display = 'none';
                button.classList.remove('expanded');
            }
        }

        // 删除确认
        function confirmDelete(type, id) {
            if (confirm(`确定要删除这个${type}吗？\nID: ${id}`)) {
                alert(`${type} ${id} 已删除（演示功能）`);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('嵌套表格演示页面加载完成');
            
            // 可以在这里添加更多初始化逻辑
            const expandButtons = document.querySelectorAll('.expand-btn');
            console.log(`找到 ${expandButtons.length} 个展开按钮`);
        });
    </script>
</body>
</html>
