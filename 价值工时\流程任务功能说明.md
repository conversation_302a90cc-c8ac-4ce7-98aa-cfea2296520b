# 流程任务功能说明

## 功能概述

新的流程任务页面实现了三级展开的表格系统：
1. **任务总表** - 显示所有合同任务的基本信息
2. **一级流程任务表** - 每个任务总表项目下的一级流程分解
3. **二级流程表格** - 每个一级流程下的二级流程分解

## 主要功能

### 1. 任务总表
- 显示合同编号、订单编号、证书类型、任务编号等基本信息
- 包含合同金额、已收金额、工况比等财务信息
- 每行最后有操作列：编辑、删除、展开按钮
- 顶部有"新增任务"按钮

### 2. 一级流程任务表
- 点击任务总表行的展开按钮后显示
- 显示任务名称、权重、金额、完成率、责任人等信息
- 金额 = 任务总表对应行的金额 × 一级流程任务的权重
- 完成率用进度条展示
- 包含操作列：编辑、删除、展开按钮
- 有"新增一级任务"按钮

### 3. 二级流程表格
- 点击一级流程任务行的展开按钮后显示
- 显示结构与一级流程类似
- 金额 = 对应一级流程的金额 × 二级流程的权重
- 包含操作列：编辑、删除按钮
- 有"新增二级任务"按钮

## 操作说明

### 展开/收起操作
1. 点击任务总表行最后的展开按钮（▶）展开一级流程任务
2. 展开后按钮变为（▼），再次点击可收起
3. 在一级流程任务行中点击展开按钮可展开二级流程任务

### 新增操作
1. **新增任务总表任务**：点击页面顶部的"新增任务"按钮
2. **新增一级任务**：在展开的一级流程区域点击"新增一级任务"按钮
3. **新增二级任务**：在展开的二级流程区域点击"新增二级任务"按钮

### 编辑/删除操作
- 每个层级的表格都有编辑和删除按钮
- 删除操作会同时删除所有子级任务
- 编辑操作可以修改任务的基本信息

## 数据结构

### 任务总表数据结构
```javascript
{
    id: 1,
    contractNo: 'AA5634',        // 合同编号
    orderNo: 'AAC3421',          // 订单编号
    certType: '3C认证',          // 证书类型
    taskNo: 'AACN2721',          // 任务编号
    contractAmount: 12300,        // 合同金额
    receivedAmount: 8450,         // 已收金额
    organization: '京分中心',     // 所属机构
    process: '认证一部',          // 流程
    department: '未封闭',         // 科组
    taskStatus: '未封闭',         // 任务状态
    workRatio: '56.89%',          // 工况比
    createTime: '2025/1/12',      // 创建时间
    responsible: '王勇',          // 负责人
    level1Tasks: [...]            // 一级任务数组
}
```

### 一级任务数据结构
```javascript
{
    id: 11,
    taskName: '市场开发',         // 任务名称
    weight: 0.1,                  // 权重
    amount: 1230,                 // 金额（自动计算）
    progress: 100,                // 完成率
    responsible: '王勇',          // 责任人
    taskStatus: '已完成',         // 任务状态
    level2Tasks: [...]            // 二级任务数组
}
```

### 二级任务数据结构
```javascript
{
    id: 111,
    taskName: '客户准护',         // 任务名称
    weight: 0.1,                  // 权重
    amount: 123,                  // 金额（自动计算）
    progress: 100,                // 完成率
    responsible: '王勇',          // 责任人
    taskStatus: '已完成'          // 任务状态
}
```

## 样式特点

1. **层级区分**：不同层级使用不同的背景色和边框样式
2. **进度条**：完成率使用可视化的进度条显示
3. **响应式设计**：支持不同屏幕尺寸的显示
4. **交互反馈**：按钮有悬停效果，展开按钮有旋转动画

## 技术实现

- 使用纯JavaScript实现动态表格渲染
- CSS3实现样式和动画效果
- 支持数据的增删改查操作
- 采用模块化的函数设计，便于维护和扩展

## 扩展建议

1. 可以添加模态框来替代简单的prompt输入
2. 可以集成后端API进行数据持久化
3. 可以添加数据导出功能
4. 可以添加搜索和筛选功能
5. 可以添加批量操作功能
