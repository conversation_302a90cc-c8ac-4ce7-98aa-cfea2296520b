<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套表格测试</title>
    <link rel="stylesheet" href="./CSS/主页.css">
    <link rel="stylesheet" href="./libs/fontawesome.css">
    <style>
        body {
            padding: 20px;
            background-color: #f1f5f9;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-body {
            padding: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        th {
            background-color: #f8fafc;
            font-weight: 600;
        }
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .edit-btn {
            background-color: #3b82f6;
            color: white;
        }
        .delete-btn {
            background-color: #ef4444;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>嵌套表格功能测试</h1>
        
        <div class="card">
            <div class="card-header">
                <h3>任务总表（嵌套表格）</h3>
                <button class="btn btn-success" onclick="alert('新增任务功能')">
                    <i class="fas fa-plus"></i> 新增任务
                </button>
            </div>
            <div class="card-body">
                <table class="expandable-table">
                    <thead>
                        <tr>
                            <th width="30px"></th>
                            <th>合同编号</th>
                            <th>订单编号</th>
                            <th>证书类型</th>
                            <th>任务编号</th>
                            <th>合同金额</th>
                            <th>已收金额</th>
                            <th>所属机构</th>
                            <th>流程</th>
                            <th>科组</th>
                            <th>任务状态</th>
                            <th>工况比</th>
                            <th>创建时间</th>
                            <th>负责人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 第一个主任务 -->
                        <tr class="main-task-row" data-task-id="AA5634">
                            <td>
                                <button class="expand-btn" onclick="toggleFirstLevelProcess('AA5634')">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </td>
                            <td>AA5634</td>
                            <td>AAC3421</td>
                            <td>3C认证</td>
                            <td>AACN2721</td>
                            <td>12300</td>
                            <td>8450</td>
                            <td>分中心认证通一部</td>
                            <td>2</td>
                            <td>未封闭</td>
                            <td>56.89%</td>
                            <td>2025/1/12</td>
                            <td>王勇</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="alert('编辑主任务: AA5634')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn delete-btn" onclick="alert('删除主任务: AA5634')">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </td>
                        </tr>
                        <!-- 一级流程子表 -->
                        <tr class="first-level-container" id="firstLevel_AA5634" style="display: none;">
                            <td colspan="15">
                                <div class="sub-table-container">
                                    <div class="sub-table-header">
                                        <h4>一级流程任务</h4>
                                        <button class="btn btn-sm btn-success" onclick="alert('新增一级流程')">
                                            <i class="fas fa-plus"></i> 新增一级流程
                                        </button>
                                    </div>
                                    <table class="sub-table first-level-table">
                                        <thead>
                                            <tr>
                                                <th width="30px"></th>
                                                <th>流程编号</th>
                                                <th>任务名称</th>
                                                <th>权重</th>
                                                <th>金额</th>
                                                <th>完成率</th>
                                                <th>责任人</th>
                                                <th>任务完成时间</th>
                                                <th>备注</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="first-level-row" data-first-level-id="AA5634_1">
                                                <td>
                                                    <button class="expand-btn" onclick="toggleSecondLevelProcess('AA5634_1')">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </td>
                                                <td>1</td>
                                                <td>市场开发</td>
                                                <td>10%</td>
                                                <td>1230</td>
                                                <td>100%</td>
                                                <td>王勇</td>
                                                <td></td>
                                                <td>已完成</td>
                                                <td>
                                                    <button class="action-btn edit-btn" onclick="alert('编辑一级流程: AA5634_1')">
                                                        <i class="fas fa-edit"></i> 编辑
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="alert('删除一级流程: AA5634_1')">
                                                        <i class="fas fa-trash-alt"></i> 删除
                                                    </button>
                                                </td>
                                            </tr>
                                            <!-- 二级流程子表 -->
                                            <tr class="second-level-container" id="secondLevel_AA5634_1" style="display: none;">
                                                <td colspan="10">
                                                    <div class="sub-table-container level-2">
                                                        <div class="sub-table-header">
                                                            <h5>二级流程任务</h5>
                                                            <button class="btn btn-sm btn-success" onclick="alert('新增二级流程')">
                                                                <i class="fas fa-plus"></i> 新增二级流程
                                                            </button>
                                                        </div>
                                                        <table class="sub-table second-level-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>流程编号</th>
                                                                    <th>任务名称</th>
                                                                    <th>负责部门</th>
                                                                    <th>责任人</th>
                                                                    <th>产值权重</th>
                                                                    <th>金额</th>
                                                                    <th>完成率</th>
                                                                    <th>任务完成时间</th>
                                                                    <th>备注</th>
                                                                    <th>操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr data-second-level-id="AA5634_1_1">
                                                                    <td>1</td>
                                                                    <td>客户维护</td>
                                                                    <td>分中心检查部</td>
                                                                    <td>王勇</td>
                                                                    <td>10%</td>
                                                                    <td>123</td>
                                                                    <td>100%</td>
                                                                    <td></td>
                                                                    <td>已完成</td>
                                                                    <td>
                                                                        <button class="action-btn edit-btn" onclick="alert('编辑二级流程: AA5634_1_1')">
                                                                            <i class="fas fa-edit"></i> 编辑
                                                                        </button>
                                                                        <button class="action-btn delete-btn" onclick="alert('删除二级流程: AA5634_1_1')">
                                                                            <i class="fas fa-trash-alt"></i> 删除
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 切换一级流程显示/隐藏
        function toggleFirstLevelProcess(taskId) {
            const container = document.getElementById(`firstLevel_${taskId}`);
            const button = document.querySelector(`[onclick="toggleFirstLevelProcess('${taskId}')"]`);
            
            if (container.style.display === 'none') {
                container.style.display = 'table-row';
                button.classList.add('expanded');
            } else {
                container.style.display = 'none';
                button.classList.remove('expanded');
                
                // 同时隐藏所有二级流程
                const secondLevelContainers = container.querySelectorAll('.second-level-container');
                secondLevelContainers.forEach(sc => {
                    sc.style.display = 'none';
                    const secondButton = sc.previousElementSibling.querySelector('.expand-btn');
                    if (secondButton) secondButton.classList.remove('expanded');
                });
            }
        }

        // 切换二级流程显示/隐藏
        function toggleSecondLevelProcess(firstLevelId) {
            const container = document.getElementById(`secondLevel_${firstLevelId}`);
            const button = document.querySelector(`[onclick="toggleSecondLevelProcess('${firstLevelId}')"]`);
            
            if (container.style.display === 'none') {
                container.style.display = 'table-row';
                button.classList.add('expanded');
            } else {
                container.style.display = 'none';
                button.classList.remove('expanded');
            }
        }
    </script>
</body>
</html>
