# 嵌套表格功能实现说明

## 功能概述

根据您的需求，我已经成功实现了三级嵌套表格系统，将原来独立的一级流程表和二级流程表整合到任务总表中，形成了层次化的数据展示结构。

## 主要功能特性

### 1. 三级嵌套结构
- **主任务表**：显示合同基本信息
- **一级流程表**：嵌入在主任务下，显示主要流程步骤
- **二级流程表**：嵌入在一级流程下，显示详细的子流程

### 2. 展开/收缩功能
- 点击左侧箭头按钮可以展开或收缩子表格
- 支持多级展开：主任务 → 一级流程 → 二级流程
- 收缩父级时自动收缩所有子级
- 箭头图标会根据展开状态旋转

### 3. 金额自动计算
- **一级流程金额** = 主任务合同金额 × 一级流程权重
- **二级流程金额** = 一级流程金额 × 二级流程产值权重
- 支持百分比权重（如"10%"）自动转换为小数计算

### 4. 完整的CRUD功能
- **新增**：每个层级都有"新增"按钮，支持添加新的流程任务
- **编辑**：每行都有编辑按钮，可以修改现有数据
- **删除**：每行都有删除按钮，带确认提示，删除父级时会删除所有子级

### 5. 模态框表单
- 为一级流程和二级流程提供了专门的模态框
- 表单包含所有必要字段
- 支持数据验证和自动计算

## 文件修改说明

### 1. HTML结构修改 (`价值工时.html`)
- 删除了独立的一级流程表和二级流程表
- 修改任务总表结构，添加展开按钮列
- 为每个主任务添加嵌套的子表格容器
- 保留了原有的模态框结构

### 2. CSS样式添加 (`CSS/主页.css`)
- 添加了嵌套表格专用样式类
- 实现了展开按钮的动画效果
- 为不同层级的表格设置了不同的背景色
- 添加了响应式设计支持

### 3. JavaScript功能实现 (`JS/主页.js`)
- 实现了展开/收缩切换函数
- 添加了金额自动计算功能
- 实现了新增、编辑、删除的处理逻辑
- 为模态框添加了保存功能

## 核心函数说明

### 展开/收缩函数
```javascript
// 切换一级流程显示/隐藏
function toggleFirstLevelProcess(taskId)

// 切换二级流程显示/隐藏
function toggleSecondLevelProcess(firstLevelId)
```

### CRUD操作函数
```javascript
// 新增功能
function addFirstLevelProcess(taskId)
function addSecondLevelProcess(firstLevelId)

// 编辑功能
function editFirstLevelProcess(button, firstLevelId)
function editSecondLevelProcess(button, secondLevelId)

// 删除功能
function deleteFirstLevelProcess(button, firstLevelId)
function deleteSecondLevelProcess(button, secondLevelId)
```

### 金额计算函数
```javascript
function calculateAmount(parentAmount, weight)
```

## 演示页面

我创建了两个演示页面供您测试：

1. **test-nested-table.html** - 基础功能测试页面
2. **demo-nested-table.html** - 完整功能演示页面

## 使用方法

1. 打开 `价值工时.html` 主页面
2. 登录后进入"流程任务"页面
3. 点击任务行左侧的箭头按钮展开一级流程
4. 点击一级流程行左侧的箭头按钮展开二级流程
5. 使用"新增"按钮添加新的流程任务
6. 使用"编辑"和"删除"按钮管理现有数据

## 技术特点

- **响应式设计**：支持不同屏幕尺寸
- **用户友好**：直观的展开/收缩操作
- **数据完整性**：自动计算金额，确保数据一致性
- **可扩展性**：易于添加新的字段和功能
- **性能优化**：使用CSS动画和高效的DOM操作

## 后续扩展建议

1. **数据持久化**：连接后端数据库保存数据
2. **权限控制**：根据用户角色限制操作权限
3. **数据导出**：支持导出Excel或PDF格式
4. **批量操作**：支持批量编辑和删除
5. **搜索过滤**：添加搜索和筛选功能

## 注意事项

- 删除父级任务时会同时删除所有子级任务
- 金额计算基于权重百分比，请确保权重设置合理
- 模态框表单包含必填字段验证
- 建议在生产环境中添加更多的错误处理和用户提示

这个嵌套表格系统完全满足了您的需求，提供了直观、高效的数据管理界面。
